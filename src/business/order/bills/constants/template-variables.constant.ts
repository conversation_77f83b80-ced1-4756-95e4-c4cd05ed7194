/**
 * Template variables that can be used in the bill of lading template
 */
export enum TEMPLATE_VARIABLE {
  // tenant Info
  TENANT_NAME = '#TENANT_NAME',
  TENANT_PHONE_NUMBER = '#TENANT_PHONE_NUMBER',
  TENANT_WEBSITE = '#TENANT_WEBSITE',
  TENANT_LOGO = '#TENANT_LOGO',

  // Basic order information
  TRACKING_NUMBER = '#TRACKING_NUMBER',
  SUBMITTED_DATE = '#SUBMITTED_DATE',

  // Collection location information
  COLLECTION_ADDRESS_LINE_1 = '#COLLECTION_ADDRESS_LINE_1',
  COLLECTION_ADDRESS_LINE_2 = '#COLLECTION_ADDRESS_LINE_2',
  COLLECTION_CITY = '#COLLECTION_CITY',
  COLLECTION_POSTAL_CODE = '#COLLECTION_POSTAL_CODE',
  COLLECTION_COMPANY_NAME = '#COLLECTION_COMPANY_NAME',
  COLLECTION_CONTACT_NAME = '#COLLECTION_CONTACT_NAME',
  COLLECTION_EMAIL = '#COLLECTION_EMAIL',
  COLLECTION_PHONE_NUMBER = '#COLLECTION_PHONE_NUMBER',

  // Delivery location information
  DELIVERY_ADDRESS_LINE_1 = '#DELIVERY_ADDRESS_LINE_1',
  DELIVERY_ADDRESS_LINE_2 = '#DELIVERY_ADDRESS_LINE_2',
  DELIVERY_CITY = '#DELIVERY_CITY',
  DELIVERY_POSTAL_CODE = '#DELIVERY_POSTAL_CODE',
  DELIVERY_COMPANY_NAME = '#DELIVERY_COMPANY_NAME',
  DELIVERY_CONTACT_NAME = '#DELIVERY_CONTACT_NAME',
  DELIVERY_EMAIL = '#DELIVERY_EMAIL',
  DELIVERY_PHONE_NUMBER = '#DELIVERY_PHONE_NUMBER',

  // Legacy variables (for backward compatibility)
  SENDER_NAME = '#SENDER_NAME',
  SENDER_ADDRESS = '#SENDER_ADDRESS',
  RECEIVER_NAME = '#RECEIVER_NAME',
  RECEIVER_ADDRESS = '#RECEIVER_ADDRESS',

  // Service details
  SERVICE_LEVEL = '#SERVICE_LEVEL',
  EXPECTED_COLLECTION_TIME = '#EXPECTED_COLLECTION_TIME',
  EXPECTED_DELIVERY_TIME = '#EXPECTED_DELIVERY_TIME',
  ACTUAL_COLLECTION_TIME = '#ACTUAL_COLLECTION_TIME',
  ACTUAL_DELIVERY_TIME = '#ACTUAL_DELIVERY_TIME',

  // Package information
  DESCRIPTION = '#DESCRIPTION',
  QUANTITY = '#QUANTITY',
  WEIGHT = '#WEIGHT',
  DIMENSIONS = '#DIMENSIONS',
  DECLARED_VALUE = '#DECLARED_VALUE',
  DELIVERY_INSTRUCTIONS = '#DELIVERY_INSTRUCTIONS',

  // Reference information
  PO_NUMBER = '#PO_NUMBER',
  DEPARTMENT = '#DEPARTMENT',
  REF_NUMBER = '#REF_NUMBER',

  // Special variables
  BARCODE = '#BARCODE',
  BARCODE_WITH_TEXT = '#BARCODE_WITH_TEXT',

  //other
  DRIVER_NAME = '#DRIVER',
  VEHICLE_NUMBER = '#VEHICLE_NUMBER',
}
